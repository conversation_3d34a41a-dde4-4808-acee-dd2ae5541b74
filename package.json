{"name": "philo-microfrontend", "version": "1.0.0", "description": "Microfrontend that consumes PageLayoutWithSidebar from philo-fe", "main": "index.js", "type": "module", "engines": {"node": "^24", "yarn": "^1.21.1"}, "scripts": {"start": "webpack serve --config webpack.config.js", "build": "webpack --config webpack.config.js", "dev": "webpack serve --mode development --config webpack.config.js", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "18.3.1", "react-dom": "18.3.1", "styled-components": "6.1.13"}, "devDependencies": {"@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@types/webpack": "5.28.5", "@typescript-eslint/eslint-plugin": "8.21.0", "@typescript-eslint/parser": "8.21.0", "@module-federation/webpack": "^0.0.5", "css-loader": "7.1.2", "eslint": "9.14.0", "eslint-plugin-react": "7.37.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "5.6.3", "style-loader": "4.0.0", "ts-loader": "9.5.2", "typescript": "5.7.3", "webpack": "5.99.9", "webpack-cli": "6.0.1", "webpack-dev-server": "5.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/tatari-tv/philo-microfrontend.git"}, "author": "", "license": "ISC"}