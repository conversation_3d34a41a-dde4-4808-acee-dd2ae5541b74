# Philo Microfrontend

A microfrontend application that consumes the `PageLayoutWithSidebar` component from philo-fe using Webpack Module Federation.

## Overview

This project demonstrates how to:
- Set up Webpack Module Federation to consume remote components
- Import and use the `PageLayoutWithSidebar` component from the main philo-fe application
- Share dependencies like React, React-DOM, and styled-components between the host and remote applications

## Prerequisites

- Node.js ^24
- Yarn ^1.21.1
- The philo-fe application running on port 3000

## Setup

1. Install dependencies:
```bash
yarn install
```

2. Start the philo-fe host application:
```bash
cd ../philo-fe
yarn install
yarn dev
```

3. Start this microfrontend application:
```bash
yarn dev
```

4. Open your browser to `http://localhost:3001` to see the microfrontend consuming the PageLayoutWithSidebar component.

## Architecture

### Module Federation Configuration

**Host (philo-fe):**
- Exposes: `PageLayoutWithSidebar` component
- Port: 3000
- Remote entry: `http://localhost:3000/remoteEntry.js`

**Remote (philo-microfrontend):**
- Consumes: `PageLayoutWithSidebar` from philo-fe
- Port: 3001
- Name: `philoMicrofrontend`

### Shared Dependencies

Both applications share the following dependencies as singletons:
- react@^18.3.1
- react-dom@^18.3.1  
- styled-components@^6.1.13

This ensures that both applications use the same instance of these libraries, preventing version conflicts and reducing bundle size.

## Development

- `yarn dev` - Start the development server with hot reloading
- `yarn build` - Create a production build
- `yarn lint` - Run ESLint
- `yarn type-check` - Run TypeScript type checking

## File Structure

```
src/
├── App.tsx          # Main application component that consumes PageLayoutWithSidebar
├── index.tsx        # Application entry point
public/
├── index.html       # HTML template
webpack.config.js    # Webpack Module Federation configuration
package.json         # Dependencies and scripts
tsconfig.json        # TypeScript configuration
```

## Usage Notes

- The microfrontend includes mock data to satisfy the PageLayoutWithSidebar component's prop requirements
- The component is lazy loaded using React.Suspense for better performance
- CORS headers are configured in the webpack dev server to allow cross-origin requests
- The application will show a loading state while the remote component is being fetched

## Troubleshooting

1. **Remote component not loading**: Ensure philo-fe is running on port 3000
2. **CORS errors**: Check that CORS headers are properly configured in both applications
3. **Version conflicts**: Verify that shared dependencies have matching version ranges
4. **Build errors**: Run `yarn type-check` to identify TypeScript issues
