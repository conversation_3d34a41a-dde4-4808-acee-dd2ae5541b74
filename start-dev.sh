#!/bin/bash

# Development startup script for the microfrontend
echo "🚀 Starting Philo Microfrontend Development Environment"
echo "=================================================="

# Check if philo-fe is already running on port 3000
if lsof -i :3000 >/dev/null 2>&1; then
    echo "✅ philo-fe appears to be running on port 3000"
else
    echo "❌ philo-fe does not appear to be running on port 3000"
    echo "Please start philo-fe first by running:"
    echo "  cd ../philo-fe && yarn dev"
    echo ""
    read -p "Press Enter when philo-fe is running, or Ctrl+C to exit..."
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    yarn install
fi

# Start the microfrontend
echo "🌟 Starting microfrontend on port 3001..."
echo "Open http://localhost:3001 in your browser"
echo ""
yarn dev
