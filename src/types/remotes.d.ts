// Type declarations for module federation remotes

declare module 'philoFe/PageLayoutWithSidebar' {
  import React from 'react';
  
  // Basic types for the component props
  interface UserCompany {
    id: string;
    name: string;
    logoUrl: string;
    slug: string;
  }

  interface BasicNavItem {
    name: string;
    path: string;
    label: string;
  }

  interface SideNavConfig {
    sections: Array<{
      name: string;
      items: BasicNavItem[];
    }>;
  }

  interface PageLayoutWithSidebarProps {
    company: UserCompany;
    userCompanies: UserCompany[];
    isLoading?: boolean;
    isRightAsideOpen?: boolean;
    rightAsideWidth?: 'small' | 'medium' | 'large';
    rightAsideContent?: React.ReactNode;
    sideNavConfig: SideNavConfig;
    sideNavHomeItem: BasicNavItem;
    isSideNavAllClientsOption?: boolean;
    children?: React.ReactNode;
    onCompanyChange: (company: UserCompany) => void;
  }

  const PageLayoutWithSidebar: React.ComponentType<PageLayoutWithSidebarProps>;
  export default PageLayoutWithSidebar;
}
