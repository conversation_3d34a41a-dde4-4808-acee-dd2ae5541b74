import React, { Suspense } from 'react';

// Dynamically import the PageLayoutWithSidebar from philo-fe
const PageLayoutWithSidebar = React.lazy(() => import('philoFe/PageLayoutWithSidebar'));

// Mock data to satisfy the component's requirements
const mockCompany = {
  id: '1',
  name: 'Test Company',
  logoUrl: 'https://via.placeholder.com/100x50',
  slug: 'test-company'
};

const mockUserCompanies = [
  {
    id: '1',
    name: 'Test Company',
    logoUrl: 'https://via.placeholder.com/100x50',
    slug: 'test-company'
  },
  {
    id: '2',
    name: 'Another Company',
    logoUrl: 'https://via.placeholder.com/100x50',
    slug: 'another-company'
  }
];

const mockSideNavConfig = {
  sections: [
    {
      name: 'Dashboard',
      items: [
        {
          name: 'Overview',
          path: '/overview',
          label: 'Overview'
        },
        {
          name: 'Analytics',
          path: '/analytics',
          label: 'Analytics'
        }
      ]
    },
    {
      name: 'Management',
      items: [
        {
          name: 'Campaigns',
          path: '/campaigns',
          label: 'Campaigns'
        },
        {
          name: 'Reports',
          path: '/reports',
          label: 'Reports'
        }
      ]
    }
  ]
};

const mockSideNavHomeItem = {
  name: 'Home',
  path: '/',
  label: 'Home'
};

const App: React.FC = () => {
  const handleCompanyChange = (company: {id: string; name: string; logoUrl: string; slug: string}) => {
    console.log('Company changed:', company);
  };

  return (
    <div>
      <h1 style={{ padding: '20px', textAlign: 'center', backgroundColor: '#f0f0f0' }}>
        Philo Microfrontend Demo
      </h1>
      <Suspense fallback={<div style={{ padding: '20px' }}>Loading PageLayoutWithSidebar...</div>}>
        <PageLayoutWithSidebar
          company={mockCompany}
          userCompanies={mockUserCompanies}
          sideNavConfig={mockSideNavConfig}
          sideNavHomeItem={mockSideNavHomeItem}
          onCompanyChange={handleCompanyChange}
        >
          <div style={{ padding: '20px' }}>
            <h2>Welcome to the Microfrontend!</h2>
            <p>
              This page is consuming the PageLayoutWithSidebar component from philo-fe
              via Webpack Module Federation.
            </p>
            <div style={{ 
              backgroundColor: '#e8f4f8', 
              padding: '15px', 
              borderRadius: '5px',
              margin: '20px 0'
            }}>
              <h3>Features demonstrated:</h3>
              <ul>
                <li>✅ Module Federation remote consumption</li>
                <li>✅ Shared React dependencies</li>
                <li>✅ PageLayoutWithSidebar component integration</li>
                <li>✅ Mock data for component props</li>
              </ul>
            </div>
            <p>
              Try interacting with the sidebar navigation to see the 
              PageLayoutWithSidebar component in action.
            </p>
          </div>
        </PageLayoutWithSidebar>
      </Suspense>
    </div>
  );
};

export default App;
